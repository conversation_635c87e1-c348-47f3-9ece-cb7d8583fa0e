import { JwtAuthGuard } from '#/common/guards/jwt-auth.guard.js';
import { Role } from '#/users/dto/role.dto.js';
import { SessionDto } from '#/users/dto/session.dto.js';
import { User } from '#/users/dto/user.dto.js';
import { UsersService } from '#/users/users.service.js';
import { Body, Controller, Delete, ForbiddenException, Get, HttpCode, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PermissionsService } from './permissions.service.js';
import { RolesService } from './roles.service.js';

@ApiTags('Admin')
@Controller('admin')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AdminController {
  constructor(
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly permissionsService: PermissionsService,
  ) {}

  @Get('users/:userId/sessions')
  @ApiOperation({ summary: 'List sessions for any user (admin only)' })
  @ApiResponse({ status: 200, description: 'List of sessions', type: [SessionDto] })
  async getUserSessions(
    @Param('userId') userId: string,
    @Req() req: any
  ): Promise<SessionDto[]> {
    if (!req.user?.roles?.includes('admin')) {
      throw new ForbiddenException('Admin only');
    }
    return this.usersService.getUserSessions(userId);
  }

  @Delete('users/:userId/sessions/:sessionId')
  @ApiOperation({ summary: 'Revoke a session for any user (admin only)' })
  @ApiResponse({ status: 200, description: 'Session revoked' })
  async revokeUserSession(
    @Param('userId') userId: string,
    @Param('sessionId') sessionId: string,
    @Req() req: any
  ): Promise<{ message: string }> {
    if (!req.user?.roles?.includes('admin')) {
      throw new ForbiddenException('Admin only');
    }
    return this.usersService.removeUserSession(userId, sessionId, req);
  }

  @Get('users')
  @ApiOperation({ summary: 'List all users' })
  @ApiResponse({ status: 200, description: 'List of users', type: [User] })
  async listUsers(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('search') search: string = '',
    @Query('status') status: string = '',
    @Query('role') role: string = '',
    @Req() req: any
  ) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminListUsers({ page, limit, search, status, role });
  }

  @Post('users')
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({ status: 201, description: 'User created', type: User })
  async createUser(@Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminCreateUser(body);
  }

  @Get('users/:userId')
  @ApiOperation({ summary: 'Get user details' })
  @ApiResponse({ status: 200, description: 'User details', type: User })
  async getUser(@Param('userId') userId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminGetUser(userId);
  }

  @Put('users/:userId')
  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({ status: 200, description: 'User updated', type: User })
  async updateUser(@Param('userId') userId: string, @Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminUpdateUser(userId, body);
  }

  @Delete('users/:userId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({ status: 204, description: 'User deleted' })
  async deleteUser(@Param('userId') userId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    await this.usersService.adminDeleteUser(userId);
  }

  @Get('roles')
  @ApiOperation({ summary: 'List all roles' })
  @ApiResponse({ status: 200, description: 'List of roles', type: [Role] })
  async listRoles(@Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.rolesService.listRoles();
  }

  @Post('roles')
  @ApiOperation({ summary: 'Create a new role' })
  @ApiResponse({ status: 201, description: 'Role created', type: Role })
  async createRole(@Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.rolesService.createRole(body);
  }

  @Get('roles/:roleId')
  @ApiOperation({ summary: 'Get role details' })
  @ApiResponse({ status: 200, description: 'Role details', type: Role })
  async getRole(@Param('roleId') roleId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.rolesService.getRole(roleId);
  }

  @Put('roles/:roleId')
  @ApiOperation({ summary: 'Update role' })
  @ApiResponse({ status: 200, description: 'Role updated', type: Role })
  async updateRole(@Param('roleId') roleId: string, @Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.rolesService.updateRole(roleId, body);
  }

  @Delete('roles/:roleId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete role' })
  @ApiResponse({ status: 204, description: 'Role deleted' })
  async deleteRole(@Param('roleId') roleId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    await this.rolesService.deleteRole(roleId);
  }

  @Get('users/:userId/roles')
  @ApiOperation({ summary: 'Get user roles' })
  @ApiResponse({ status: 200, description: 'User roles', type: [Role] })
  async getUserRoles(@Param('userId') userId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminListUserRoles(userId);
  }

  @Post('users/:userId/roles')
  @ApiOperation({ summary: 'Assign role to user' })
  @ApiResponse({ status: 200, description: 'Role assigned' })
  async assignRoleToUser(@Param('userId') userId: string, @Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.usersService.adminAssignRoleToUser(userId, body.roleId);
  }

  @Delete('users/:userId/roles/:roleId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Remove role from user' })
  @ApiResponse({ status: 204, description: 'Role removed' })
  async removeRoleFromUser(@Param('userId') userId: string, @Param('roleId') roleId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    await this.usersService.adminRemoveRoleFromUser(userId, roleId);
  }

  @Get('permissions')
  @ApiOperation({ summary: 'List all permissions' })
  @ApiResponse({ status: 200, description: 'List of permissions', type: [Object] })
  async listPermissions(@Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.permissionsService.listPermissions();
  }

  @Post('permissions')
  @ApiOperation({ summary: 'Create a new permission' })
  @ApiResponse({ status: 201, description: 'Permission created', type: Object })
  async createPermission(@Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.permissionsService.createPermission(body);
  }

  @Get('permissions/:permissionId')
  @ApiOperation({ summary: 'Get permission details' })
  @ApiResponse({ status: 200, description: 'Permission details', type: Object })
  async getPermission(@Param('permissionId') permissionId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.permissionsService.getPermission(permissionId);
  }

  @Put('permissions/:permissionId')
  @ApiOperation({ summary: 'Update permission' })
  @ApiResponse({ status: 200, description: 'Permission updated', type: Object })
  async updatePermission(@Param('permissionId') permissionId: string, @Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.permissionsService.updatePermission(permissionId, body);
  }

  @Delete('permissions/:permissionId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Delete permission' })
  @ApiResponse({ status: 204, description: 'Permission deleted' })
  async deletePermission(@Param('permissionId') permissionId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    await this.permissionsService.deletePermission(permissionId);
  }

  @Post('roles/:roleId/permissions')
  @ApiOperation({ summary: 'Assign permission to role' })
  @ApiResponse({ status: 200, description: 'Permission assigned' })
  async assignPermissionToRole(@Param('roleId') roleId: string, @Body() body: any, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    return this.rolesService.assignPermissionToRole(roleId, body.permissionId);
  }

  @Delete('roles/:roleId/permissions/:permissionId')
  @HttpCode(204)
  @ApiOperation({ summary: 'Remove permission from role' })
  @ApiResponse({ status: 204, description: 'Permission removed' })
  async removePermissionFromRole(@Param('roleId') roleId: string, @Param('permissionId') permissionId: string, @Req() req: any) {
    if (!req.user?.roles?.includes('admin')) throw new ForbiddenException('Admin only');
    await this.rolesService.removePermissionFromRole(roleId, permissionId);
  }
}