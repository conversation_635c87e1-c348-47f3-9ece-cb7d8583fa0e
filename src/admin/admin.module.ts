import { DatabaseModule } from '#/database/database.module.js';
import { UsersModule } from '#/users/users.module.js';
import { Module } from '@nestjs/common';
import { AdminController } from './admin.controller.js';
import { PermissionsService } from './permissions.service.js';
import { RolesService } from './roles.service.js';

@Module({
  imports: [UsersModule, DatabaseModule],
  controllers: [AdminController],
  providers: [RolesService, PermissionsService],
})
export class AdminModule {} 