import { User, users } from '#/database/schema/index.js';
import { userSessions } from '#/database/schema/user-sessions.schema.js';
// import { MailerService } from '@nestjs-modules/mailer';
import { BadRequestException, ConflictException, Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { randomUUID } from 'crypto';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { AuthResponse } from './dto/auth-response.dto.js';
import { LoginRequest } from './dto/login-request.dto.js';
import { RegisterRequest } from './dto/register-request.dto.js';
import { TokenResponse } from './dto/token-response.dto.js';

@Injectable()
export class AuthService {
  constructor(
    @Inject('DB') private readonly db: PostgresJsDatabase<typeof import('#/database/schema/index.js')>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    // private readonly mailerService: MailerService,
  ) {}

  async registerUser(registerData: RegisterRequest): Promise<AuthResponse> {
    const { email, password, name } = registerData;

    // Check if user already exists
    const existingUser = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (existingUser.length > 0) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    // Create user
    const [savedUser] = await this.db.insert(users).values({
      email,
      password: hashedPassword,
      name,
      emailVerificationToken,
      roles: ['user'], // Default role
      isActive: true,
      emailVerified: false,
    }).returning();

    // Generate tokens
    const sessionId = randomUUID();
    const tokens = await this.generateTokens(savedUser, sessionId);

    // Send verification email
    const frontendUrl = this.configService.get('FRONTEND_URL', 'https://rsglider.com');
    // await this.mailerService.sendMail({
    //   to: email,
    //   subject: `Verify your email address for ${siteName}`,
    //   html: `
    //     <div style="font-family: Arial, sans-serif; max-width: 480px; margin: 0 auto; border: 1px solid #eee; border-radius: 8px; padding: 32px 24px; background: #fafbfc;">
    //       <h2 style="color: #2d3748; margin-bottom: 16px;">Welcome to ${siteName}!</h2>
    //       <p style="color: #4a5568; font-size: 16px;">Hi${name ? ` ${name}` : ''},</p>
    //       <p style="color: #4a5568; font-size: 16px;">Thank you for registering. Please verify your email address to activate your account.</p>
    //       <a href="${verifyUrl}" style="display: inline-block; margin: 24px 0; padding: 12px 24px; background: #3182ce; color: #fff; border-radius: 4px; text-decoration: none; font-weight: bold;">Verify Email</a>
    //       <p style="color: #718096; font-size: 14px;">If you did not create an account, you can safely ignore this email.</p>
    //       <hr style="margin: 32px 0; border: none; border-top: 1px solid #e2e8f0;">
    //       <p style="color: #a0aec0; font-size: 12px; text-align: center;">&copy; ${new Date().getFullYear()} ${siteName}. All rights reserved.</p>
    //     </div>
    //   `
    // });

    return {
      user: {
        id: savedUser.id,
        email: savedUser.email,
        name: savedUser.name,
        roles: savedUser.roles,
        emailVerified: savedUser.emailVerified,
      },
      ...tokens,
    };
  }

  async loginUser(loginData: LoginRequest): Promise<AuthResponse> {
    const { email, password, deviceInfo, location, platform } = loginData as any;

    // Find user
    const [user] = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    await this.db.update(users)
      .set({
        lastLoginAt: new Date(),
        lastLoginIp: 'TODO: Get IP from request',
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    // Set all other sessions to isCurrent=false
    await this.db.update(userSessions)
      .set({ isCurrent: false })
      .where(eq(userSessions.userId, user.id));

    // Generate a sessionId
    const sessionId = randomUUID();

    // Create session
    const now = new Date();
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '15m');
    const expiresAt = new Date(now.getTime() + this.parseExpirationToSeconds(expiresIn) * 1000);
    await this.db.insert(userSessions).values({
      id: sessionId,
      userId: user.id,
      platform: platform || 'web',
      deviceInfo: deviceInfo || {},
      location: location || {},
      expiresAt,
      isActive: true,
      isCurrent: true,
      requiresVerification: false,
      lastActivityAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Generate tokens with sessionId
    const tokens = await this.generateTokens(user, sessionId);

    return {
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        roles: user.roles,
        emailVerified: user.emailVerified,
      },
      ...tokens,
    };
  }

  async logoutUser(user: { id: string, sid?: string }, req: any): Promise<{ message: string }> {
    // Revoke only the session matching the sid in the JWT
    if (!user.sid) {
      throw new BadRequestException('No session ID found in token');
    }
    const [session] = await this.db
      .select()
      .from(userSessions)
      .where(and(eq(userSessions.userId, user.id), eq(userSessions.id, user.sid), eq(userSessions.isActive, true)))
      .limit(1);
    if (session) {
      const now = new Date();
      await this.db.update(userSessions)
        .set({
          isActive: false,
          revokedAt: now,
          revokedByIp: req?.ip || null,
          revokedReason: 'User logout',
          updatedAt: now,
        })
        .where(eq(userSessions.id, session.id));
    }
    return { message: 'Logged out successfully' };
  }

  async refreshToken(refreshTokenData: { refreshToken: string }): Promise<TokenResponse> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify(refreshTokenData.refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });
      // Find user
      const [user] = await this.db.select().from(users).where(eq(users.id, payload.sub)).limit(1);
      if (!user || !user.isActive) {
        throw new UnauthorizedException('Invalid refresh token');
      }
      // Use sessionId from payload if present, else generate new
      const sessionId = payload.sid || randomUUID();
      // Generate new tokens
      return await this.generateTokens(user, sessionId);
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async forgotPassword(forgotPasswordData: { email: string }): Promise<{ message: string }> {
    const { email } = forgotPasswordData;

    const [user] = await this.db.select().from(users).where(eq(users.email, email)).limit(1);
    if (!user) {
      // Don't reveal if email exists or not
      return { message: 'If the email exists, a password reset link has been sent' };
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    await this.db.update(users)
      .set({
        passwordResetToken: resetToken,
        passwordResetExpires: resetExpires,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    // Send password reset email
    // await this.mailerService.sendMail({
    //   to: email,
    //   subject: 'Reset your password',
    //   html: `<p>You requested a password reset.</p><p>Click the link below to reset your password:</p><p><a href="${resetUrl}">${resetUrl}</a></p>`
    // });

    return { message: 'If the email exists, a password reset link has been sent' };
  }

  async resetPassword(resetData: { token: string; password: string }): Promise<{ message: string }> {
    const { token, password } = resetData;

    const [user] = await this.db.select().from(users).where(eq(users.passwordResetToken, token)).limit(1);

    if (!user || !user.passwordResetExpires || user.passwordResetExpires < new Date()) {
      throw new BadRequestException('Invalid or expired reset token');
    }

    // Hash new password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Update user
    await this.db.update(users)
      .set({
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpires: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, user.id));

    return { message: 'Password reset successfully' };
  }

  private async generateTokens(user: User, sessionId: string): Promise<TokenResponse> {
    const payload = {
      sub: user.id,
      email: user.email,
      roles: user.roles,
      sid: sessionId,
    };
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '15m');
    const refreshExpiresIn = this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d');
    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('JWT_SECRET'),
        expiresIn,
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
        expiresIn: refreshExpiresIn,
      }),
    ]);
    // Convert expiration time to seconds
    const expiresInSeconds = this.parseExpirationToSeconds(expiresIn);
    return {
      accessToken,
      refreshToken,
      expiresIn: expiresInSeconds,
      tokenType: 'Bearer',
    };
  }

  private parseExpirationToSeconds(expiration: string): number {
    // Parse common time formats like '15m', '1h', '7d', etc.
    const match = expiration.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 900; // Default to 15 minutes
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 60 * 60 * 24;
      default: return 900;
    }
  }
}
