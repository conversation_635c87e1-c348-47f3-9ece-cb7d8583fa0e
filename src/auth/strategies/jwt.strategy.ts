import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../auth.service.js';

export interface JwtPayload {
  sub: string;
  email: string;
  roles: string[];
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: JwtPayload & { sid?: string }) {
    // The payload is already validated by passport-jwt
    // We can add additional validation here if needed
    
    // For now, we'll just return the user info from the payload
    // In a more complex setup, you might want to fetch the user from the database
    // to ensure they still exist and are active
    
    return {
      id: payload.sub,
      email: payload.email,
      roles: payload.roles,
      sid: payload.sid,
    };
  }
}
