import { ClientReleasesService, TauriUpdateResponse } from '#/common/services/client-releases.service.js';
import { Controller, Get, HttpCode, HttpStatus, Logger, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Client Updates')
@Controller('client')
export class ClientController {
  private readonly logger = new Logger(ClientController.name);

  constructor(private readonly clientReleasesService: ClientReleasesService) {}

  @Get('check_update/:target/:arch/:current_version')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check for client updates',
    description: `
      Check for available updates for the RSGlider desktop client.
      This endpoint follows the Tauri updater specification and returns update information
      if a newer version is available, or 204 No Content if no update is needed.
      
      The endpoint supports both stable and beta release channels.
    `,
  })
  @ApiParam({
    name: 'target',
    description: 'Target platform',
    enum: ['windows', 'macos', 'linux'],
    example: 'windows',
  })
  @ApiParam({
    name: 'arch',
    description: 'Target architecture',
    enum: ['x64', 'arm64'],
    example: 'x64',
  })
  @ApiParam({
    name: 'current_version',
    description: 'Current client version (semantic versioning)',
    example: '1.0.0',
  })
  @ApiQuery({
    name: 'channel',
    description: 'Release channel',
    enum: ['stable', 'beta'],
    required: false,
    example: 'stable',
  })
  @ApiResponse({
    status: 200,
    description: 'Update available',
    schema: {
      type: 'object',
      properties: {
        version: {
          type: 'string',
          description: 'New version number (semantic versioning)',
          example: '1.1.0',
        },
        notes: {
          type: 'string',
          description: 'Release notes for the update',
          example: 'Bug fixes and performance improvements',
        },
        pub_date: {
          type: 'string',
          format: 'date-time',
          description: 'Publication date in RFC 3339 format',
          example: '2024-01-15T10:30:00Z',
        },
        url: {
          type: 'string',
          format: 'uri',
          description: 'Signed download URL for the update bundle',
          example: 'https://releases.rsglider.com/signed-url-here',
        },
        signature: {
          type: 'string',
          description: 'Digital signature for update verification',
          example: 'dW50cnVzdGVkIGNvbW1lbnQ6IHNpZ25hdHVyZSBmcm9tIHRhdXJpIHNlY3JldCBrZXkK...',
        },
      },
      required: ['version', 'pub_date', 'url', 'signature'],
    },
  })
  @ApiResponse({
    status: 204,
    description: 'No update available - client is up to date',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid parameters (unsupported platform/arch combination)',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async checkUpdate(
    @Param('target') target: string,
    @Param('arch') arch: string,
    @Param('current_version') currentVersion: string,
    @Query('channel') channel: 'stable' | 'beta' = 'stable',
  ): Promise<TauriUpdateResponse | void> {
    this.logger.log(
      `Update check: ${target}/${arch}/${currentVersion} (${channel})`
    );

    try {
      const updateInfo = await this.clientReleasesService.checkForUpdate({
        target,
        arch,
        current_version: currentVersion,
        channel,
      });

      if (updateInfo) {
        this.logger.log(
          `Update available: ${currentVersion} -> ${updateInfo.version}`
        );
        return updateInfo;
      } else {
        this.logger.log(`No update available for ${currentVersion}`);
        // Return 204 No Content by not returning anything
        return;
      }
    } catch (error) {
      this.logger.error('Failed to check for updates', error);
      throw error;
    }
  }

  @Get('check_update')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Check for client updates (query parameters)',
    description: `
      Alternative endpoint for checking updates using query parameters instead of path parameters.
      This provides more flexibility for different client implementations.
    `,
  })
  @ApiQuery({
    name: 'target',
    description: 'Target platform',
    enum: ['windows', 'macos', 'linux'],
    example: 'windows',
  })
  @ApiQuery({
    name: 'arch',
    description: 'Target architecture', 
    enum: ['x64', 'arm64'],
    example: 'x64',
  })
  @ApiQuery({
    name: 'current_version',
    description: 'Current client version',
    example: '1.0.0',
  })
  @ApiQuery({
    name: 'channel',
    description: 'Release channel',
    enum: ['stable', 'beta'],
    required: false,
    example: 'stable',
  })
  @ApiResponse({
    status: 200,
    description: 'Update available',
    schema: {
      $ref: '#/components/schemas/TauriUpdateResponse',
    },
  })
  @ApiResponse({
    status: 204,
    description: 'No update available',
  })
  async checkUpdateQuery(
    @Query('target') target: string,
    @Query('arch') arch: string,
    @Query('current_version') currentVersion: string,
    @Query('channel') channel: 'stable' | 'beta' = 'stable',
  ): Promise<TauriUpdateResponse | void> {
    return this.checkUpdate(target, arch, currentVersion, channel);
  }
}
