import { DatabaseModule } from './database/database.module.js';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ClientReleasesService } from './services/client-releases.service.js';
import { FileUploadsService } from './services/file-uploads.service.js';
import { S3Service } from './services/s3.service.js';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [S3Service, ClientReleasesService, FileUploadsService],
  exports: [S3Service, ClientReleasesService, FileUploadsService],
})
export class CommonModule {}