import { giteaProfiles, users, type GiteaProfile, type NewGiteaProfile, type User } from '#/database/schema/index.js';
import { BadRequestException, ConflictException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { DatabaseService } from './database.service.js';
import { GiteaService } from './gitea.service.js';

export interface CreateDeveloperOptions {
  userId: string;
  giteaUsername?: string;
  giteaPassword?: string;
  autoProvision?: boolean;
}

export interface DeveloperProfileData {
  user: User;
  giteaProfile?: GiteaProfile;
  isProvisioned: boolean;
  repositoryCount: number;
  publishedCount: number;
}

@Injectable()
export class DeveloperManagementService {
  private readonly logger = new Logger(DeveloperManagementService.name);

  constructor(
    private readonly db: DatabaseService,
    private readonly giteaService: GiteaService,
  ) {}

  /**
   * Create a developer profile and optionally provision Gitea account
   */
  async createDeveloper(options: CreateDeveloperOptions): Promise<DeveloperProfileData> {
    const { userId, giteaUsername, giteaPassword, autoProvision = true } = options;

    // Get the user
    const user = await this.getUser(userId);
    
    // Check if developer profile already exists
    const existingProfile = await this.db.db
      .select()
      .from(giteaProfiles)
      .where(eq(giteaProfiles.userId, userId))
      .limit(1);

    if (existingProfile.length > 0) {
      throw new ConflictException('Developer profile already exists for this user');
    }

    // Generate Gitea username if not provided
    const finalGiteaUsername = giteaUsername || this.generateGiteaUsername(user.name || user.email);
    
    let giteaProfile: GiteaProfile | undefined;
    let isProvisioned = false;

    if (autoProvision) {
      try {
        // Create Gitea account
        const giteaUser = await this.giteaService.createUser({
          username: finalGiteaUsername,
          email: user.email,
          password: giteaPassword || this.generateRandomPassword(),
          full_name: user.name || finalGiteaUsername,
          send_notify: false,
          must_change_password: true,
          restricted: false,
          visibility: 'public',
        });

        // Create local profile record
        const newProfile: NewGiteaProfile = {
          userId: userId,
          giteaUserId: giteaUser.id,
          giteaUsername: giteaUser.login,
          giteaEmail: giteaUser.email,
          giteaFullName: giteaUser.full_name,
          giteaAvatarUrl: giteaUser.avatar_url,
          isActive: true,
          isProvisioned: true,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          totalRepositories: 0,
          publicRepositories: 0,
          privateRepositories: 0,
          publishedRepositories: 0,
          giteaProfile: {
            bio: giteaUser.description,
            website: giteaUser.website,
            location: giteaUser.location,
            followers: giteaUser.followers_count,
            following: giteaUser.following_count,
            starredRepos: giteaUser.starred_repos_count,
          },
        };

        const insertedProfiles = await this.db.db
          .insert(giteaProfiles)
          .values(newProfile)
          .returning();

        giteaProfile = insertedProfiles[0];
        isProvisioned = true;

        this.logger.log(`Created developer profile for user ${userId} with Gitea account ${finalGiteaUsername}`);
      } catch (error) {
        this.logger.error(`Failed to provision Gitea account for user ${userId}:`, error.message);
        
        // Create profile without Gitea provisioning
        const newProfile: NewGiteaProfile = {
          userId: userId,
          giteaUserId: 0, // Placeholder
          giteaUsername: finalGiteaUsername,
          giteaEmail: user.email,
          giteaFullName: user.name || finalGiteaUsername,
          isActive: false,
          isProvisioned: false,
          syncStatus: 'failed',
          syncErrors: [error.message],
          totalRepositories: 0,
          publicRepositories: 0,
          privateRepositories: 0,
          publishedRepositories: 0,
        };

        const insertedProfiles = await this.db.db
          .insert(giteaProfiles)
          .values(newProfile)
          .returning();

        giteaProfile = insertedProfiles[0];
      }
    } else {
      // Create profile without Gitea provisioning
      const newProfile: NewGiteaProfile = {
        userId: userId,
        giteaUserId: 0, // Placeholder
        giteaUsername: finalGiteaUsername,
        giteaEmail: user.email,
        giteaFullName: user.name || finalGiteaUsername,
        isActive: false,
        isProvisioned: false,
        syncStatus: 'pending',
        totalRepositories: 0,
        publicRepositories: 0,
        privateRepositories: 0,
        publishedRepositories: 0,
      };

      const insertedProfiles = await this.db.db
        .insert(giteaProfiles)
        .values(newProfile)
        .returning();

      giteaProfile = insertedProfiles[0];
    }

    return {
      user,
      giteaProfile,
      isProvisioned,
      repositoryCount: 0,
      publishedCount: 0,
    };
  }

  /**
   * Get developer profile by user ID
   */
  async getDeveloperProfile(userId: string): Promise<DeveloperProfileData | null> {
    const user = await this.getUser(userId);

    const profile = await this.db.db
      .select()
      .from(giteaProfiles)
      .where(eq(giteaProfiles.userId, userId))
      .limit(1);

    if (profile.length === 0) {
      return null;
    }

    const giteaProfile = profile[0];

    return {
      user,
      giteaProfile,
      isProvisioned: giteaProfile.isProvisioned,
      repositoryCount: giteaProfile.totalRepositories || 0,
      publishedCount: giteaProfile.publishedRepositories || 0,
    };
  }

  /**
   * Provision Gitea account for existing developer profile
   */
  async provisionGiteaAccount(userId: string, password?: string): Promise<DeveloperProfileData> {
    const profile = await this.getDeveloperProfile(userId);
    
    if (!profile) {
      throw new NotFoundException('Developer profile not found');
    }

    if (profile.isProvisioned) {
      throw new ConflictException('Gitea account already provisioned');
    }

    try {
      // Create Gitea account
      const giteaUser = await this.giteaService.createUser({
        username: profile.giteaProfile!.giteaUsername,
        email: profile.giteaProfile!.giteaEmail,
        password: password || this.generateRandomPassword(),
        full_name: profile.giteaProfile!.giteaFullName || profile.giteaProfile!.giteaUsername,
        send_notify: false,
        must_change_password: true,
        restricted: false,
        visibility: 'public',
      });

      // Update profile with Gitea information
      const updatedProfiles = await this.db.db
        .update(giteaProfiles)
        .set({
          giteaUserId: giteaUser.id,
          giteaUsername: giteaUser.login,
          giteaEmail: giteaUser.email,
          giteaFullName: giteaUser.full_name,
          giteaAvatarUrl: giteaUser.avatar_url,
          isActive: true,
          isProvisioned: true,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          syncErrors: null,
          giteaProfile: {
            bio: giteaUser.description,
            website: giteaUser.website,
            location: giteaUser.location,
            followers: giteaUser.followers_count,
            following: giteaUser.following_count,
            starredRepos: giteaUser.starred_repos_count,
          },
          updatedAt: new Date(),
        })
        .where(eq(giteaProfiles.userId, userId))
        .returning();

      const updatedProfile = updatedProfiles[0];

      this.logger.log(`Provisioned Gitea account for user ${userId}: ${giteaUser.login}`);

      return {
        user: profile.user,
        giteaProfile: updatedProfile,
        isProvisioned: true,
        repositoryCount: updatedProfile.totalRepositories || 0,
        publishedCount: updatedProfile.publishedRepositories || 0,
      };
    } catch (error) {
      // Update profile with error
      await this.db.db
        .update(giteaProfiles)
        .set({
          syncStatus: 'failed',
          syncErrors: [error.message],
          updatedAt: new Date(),
        })
        .where(eq(giteaProfiles.userId, userId));

      this.logger.error(`Failed to provision Gitea account for user ${userId}:`, error.message);
      throw new BadRequestException(`Failed to provision Gitea account: ${error.message}`);
    }
  }

  /**
   * Sync developer profile with Gitea
   */
  async syncDeveloperProfile(userId: string): Promise<DeveloperProfileData> {
    const profile = await this.getDeveloperProfile(userId);
    
    if (!profile || !profile.isProvisioned) {
      throw new NotFoundException('Provisioned developer profile not found');
    }

    try {
      // Get updated Gitea user information
      const giteaUser = await this.giteaService.getUserByUsername(profile.giteaProfile!.giteaUsername);
      
      // Get user repositories
      const repositories = await this.giteaService.getUserRepositories(profile.giteaProfile!.giteaUsername);
      
      const publicRepos = repositories.filter(repo => !repo.private).length;
      const privateRepos = repositories.filter(repo => repo.private).length;

      // Update profile with latest information
      const updatedProfiles = await this.db.db
        .update(giteaProfiles)
        .set({
          giteaFullName: giteaUser.full_name,
          giteaAvatarUrl: giteaUser.avatar_url,
          totalRepositories: repositories.length,
          publicRepositories: publicRepos,
          privateRepositories: privateRepos,
          syncStatus: 'completed',
          lastSyncAt: new Date(),
          syncErrors: null,
          giteaProfile: {
            bio: giteaUser.description,
            website: giteaUser.website,
            location: giteaUser.location,
            followers: giteaUser.followers_count,
            following: giteaUser.following_count,
            starredRepos: giteaUser.starred_repos_count,
          },
          updatedAt: new Date(),
        })
        .where(eq(giteaProfiles.userId, userId))
        .returning();

      const updatedProfile = updatedProfiles[0];

      this.logger.log(`Synced developer profile for user ${userId}`);

      return {
        user: profile.user,
        giteaProfile: updatedProfile,
        isProvisioned: true,
        repositoryCount: updatedProfile.totalRepositories || 0,
        publishedCount: updatedProfile.publishedRepositories || 0,
      };
    } catch (error) {
      // Update profile with error
      await this.db.db
        .update(giteaProfiles)
        .set({
          syncStatus: 'failed',
          syncErrors: [error.message],
          updatedAt: new Date(),
        })
        .where(eq(giteaProfiles.userId, userId));

      this.logger.error(`Failed to sync developer profile for user ${userId}:`, error.message);
      throw new BadRequestException(`Failed to sync developer profile: ${error.message}`);
    }
  }

  /**
   * Delete developer profile and optionally Gitea account
   */
  async deleteDeveloper(userId: string, deleteGiteaAccount: boolean = false): Promise<void> {
    const profile = await this.getDeveloperProfile(userId);
    
    if (!profile) {
      throw new NotFoundException('Developer profile not found');
    }

    if (deleteGiteaAccount && profile.isProvisioned) {
      try {
        await this.giteaService.deleteUser(profile.giteaProfile!.giteaUsername);
        this.logger.log(`Deleted Gitea account: ${profile.giteaProfile!.giteaUsername}`);
      } catch (error) {
        this.logger.error(`Failed to delete Gitea account ${profile.giteaProfile!.giteaUsername}:`, error.message);
        // Continue with profile deletion even if Gitea deletion fails
      }
    }

    // Delete local profile
    await this.db.db
      .delete(giteaProfiles)
      .where(eq(giteaProfiles.userId, userId));

    this.logger.log(`Deleted developer profile for user ${userId}`);
  }

  // Private helper methods
  private async getUser(userId: string): Promise<User> {
    const userResult = await this.db.db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (userResult.length === 0) {
      throw new NotFoundException('User not found');
    }

    return userResult[0];
  }

  private generateGiteaUsername(nameOrEmail: string): string {
    // Extract username from email or clean up name
    const base = nameOrEmail.includes('@') 
      ? nameOrEmail.split('@')[0] 
      : nameOrEmail;
    
    // Clean up the username (remove special characters, spaces, etc.)
    const cleaned = base
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 20);
    
    // Add random suffix to avoid conflicts
    const suffix = Math.random().toString(36).substring(2, 6);
    
    return `${cleaned}${suffix}`;
  }

  private generateRandomPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }
}
