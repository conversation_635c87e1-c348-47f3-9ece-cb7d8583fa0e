import * as schema from '#/database/schema/index.js';
import {
  giteaProfiles,
  giteaRepositories,
  type GiteaProfile
} from '#/database/schema/index.js';
import { WebhookProcessingResultDto } from '#/webhooks/dto/webhook-response.dto.js';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as crypto from 'crypto';
import { and, eq } from 'drizzle-orm';
import type { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { RepositorySyncService } from './repository-sync.service.js';

export interface GiteaWebhookPayload {
  action: string;
  number?: number;
  repository: {
    id: number;
    name: string;
    full_name: string;
    owner: {
      id: number;
      login: string;
      full_name: string;
      email: string;
      avatar_url: string;
      language: string;
      is_admin: boolean;
      last_login: string;
      created: string;
      restricted: boolean;
      active: boolean;
      prohibit_login: boolean;
      location: string;
      website: string;
      description: string;
      visibility: string;
      followers_count: number;
      following_count: number;
      starred_repos_count: number;
      username: string;
    };
    private: boolean;
    html_url: string;
    clone_url: string;
    ssh_url: string;
    description: string;
    website: string;
    stars_count: number;
    forks_count: number;
    watchers_count: number;
    open_issues_count: number;
    default_branch: string;
    created_at: string;
    updated_at: string;
    pushed_at: string;
  };
  sender: {
    id: number;
    login: string;
    full_name: string;
    email: string;
    avatar_url: string;
    language: string;
    is_admin: boolean;
    last_login: string;
    created: string;
    restricted: boolean;
    active: boolean;
    prohibit_login: boolean;
    location: string;
    website: string;
    description: string;
    visibility: string;
    followers_count: number;
    following_count: number;
    starred_repos_count: number;
    username: string;
  };
}


@Injectable()
export class WebhookProcessorService {
  private readonly logger = new Logger(WebhookProcessorService.name);
  private readonly webhookSecret: string;

  constructor(
    private readonly configService: ConfigService,
    @Inject('DB') private readonly db: NodePgDatabase<typeof schema>,
    private readonly repositorySyncService: RepositorySyncService,
  ) {
    this.webhookSecret = this.configService.get<string>('GITEA_WEBHOOK_SECRET');
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload: string, signature: string): boolean {
    if (!this.webhookSecret) {
      this.logger.warn('Webhook secret not configured, skipping signature verification');
      return true;
    }

    if (!signature) {
      this.logger.error('No signature provided in webhook');
      return false;
    }

    // Gitea uses SHA256 HMAC
    const expectedSignature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(payload)
      .digest('hex');

    const providedSignature = signature.replace('sha256=', '');

    const isValid = crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(providedSignature, 'hex')
    );

    if (!isValid) {
      this.logger.error('Webhook signature verification failed');
    }

    return isValid;
  }

  /**
   * Process repository webhook
   */
  async processRepositoryWebhook(payload: GiteaWebhookPayload): Promise<WebhookProcessingResultDto> {
    const { action, repository, sender } = payload;

    this.logger.log(`Processing repository webhook: ${action} for ${repository.full_name} by ${sender.login}`);

    try {
      // Find the developer profile for this repository owner
      const profile = await this.findDeveloperProfile(repository.owner.login);

      if (!profile) {
        return {
          processed: false,
          action,
          repository: repository.full_name,
          message: `Developer profile not found for ${repository.owner.login}`,
        };
      }

      switch (action) {
        case 'created':
          return await this.handleRepositoryCreated(profile, payload);

        case 'deleted':
          return await this.handleRepositoryDeleted(profile, payload);

        case 'edited':
        case 'updated':
          return await this.handleRepositoryUpdated(profile, payload);

        default:
          return {
            processed: false,
            action,
            repository: repository.full_name,
            message: `Unhandled repository action: ${action}`,
          };
      }
    } catch (error) {
      this.logger.error(`Failed to process repository webhook for ${repository.full_name}:`, error.message);
      return {
        processed: false,
        action,
        repository: repository.full_name,
        message: `Processing failed: ${error.message}`,
      };
    }
  }

  /**
   * Process push webhook
   */
  async processPushWebhook(payload: any): Promise<WebhookProcessingResultDto> {
    const { repository, pusher, commits } = payload;

    this.logger.log(`Processing push webhook: ${commits?.length || 0} commits to ${repository.full_name} by ${pusher.login}`);

    try {
      // Find the developer profile
      const profile = await this.findDeveloperProfile(repository.owner.login);

      if (!profile) {
        return {
          processed: false,
          action: 'push',
          repository: repository.full_name,
          message: `Developer profile not found for ${repository.owner.login}`,
        };
      }

      // Sync the repository to update latest information
      await this.repositorySyncService.syncRepository(profile.id, repository);

      // Check if this push includes marketplace metadata changes
      const hasMarketplaceChanges = commits?.some((commit: any) =>
        commit.added?.some((file: string) => file.includes('marketplace.')) ||
        commit.modified?.some((file: string) => file.includes('marketplace.')) ||
        commit.removed?.some((file: string) => file.includes('marketplace.'))
      );

      if (hasMarketplaceChanges) {
        this.logger.log(`Marketplace metadata changes detected in ${repository.full_name}`);
        // TODO: Re-parse marketplace metadata
      }

      return {
        processed: true,
        action: 'push',
        repository: repository.full_name,
        message: `Processed push with ${commits?.length || 0} commits`,
      };
    } catch (error) {
      this.logger.error(`Failed to process push webhook for ${repository.full_name}:`, error.message);
      return {
        processed: false,
        action: 'push',
        repository: repository.full_name,
        message: `Processing failed: ${error.message}`,
      };
    }
  }

  /**
   * Process release webhook
   */
  async processReleaseWebhook(payload: any): Promise<WebhookProcessingResultDto> {
    const { action, release, repository } = payload;

    this.logger.log(`Processing release webhook: ${action} for ${repository.full_name} - ${release.tag_name}`);

    try {
      // Find the developer profile
      const profile = await this.findDeveloperProfile(repository.owner.login);

      if (!profile) {
        return {
          processed: false,
          action: `release_${action}`,
          repository: repository.full_name,
          message: `Developer profile not found for ${repository.owner.login}`,
        };
      }

      // Sync the repository to update latest information
      await this.repositorySyncService.syncRepository(profile.id, repository);

      if (action === 'published') {
        this.logger.log(`New release published: ${repository.full_name} - ${release.tag_name}`);
        // TODO: Handle marketplace item version updates
      }

      return {
        processed: true,
        action: `release_${action}`,
        repository: repository.full_name,
        message: `Processed release ${action}: ${release.tag_name}`,
      };
    } catch (error) {
      this.logger.error(`Failed to process release webhook for ${repository.full_name}:`, error.message);
      return {
        processed: false,
        action: `release_${action}`,
        repository: repository.full_name,
        message: `Processing failed: ${error.message}`,
      };
    }
  }

  // Private helper methods
  private async findDeveloperProfile(giteaUsername: string): Promise<GiteaProfile | null> {
    const profiles = await this.db
      .select()
      .from(giteaProfiles)
      .where(eq(giteaProfiles.giteaUsername, giteaUsername))
      .limit(1);

    return profiles.length > 0 ? profiles[0] : null;
  }

  private async handleRepositoryCreated(
    profile: GiteaProfile,
    payload: GiteaWebhookPayload
  ): Promise<WebhookProcessingResultDto> {
    const { repository } = payload;

    // Sync the new repository
    await this.repositorySyncService.syncRepository(profile.id, repository as any);

    this.logger.log(`Repository created and synced: ${repository.full_name}`);

    return {
      processed: true,
      action: 'created',
      repository: repository.full_name,
      message: 'Repository created and synced successfully',
    };
  }

  private async handleRepositoryDeleted(
    profile: GiteaProfile,
    payload: GiteaWebhookPayload
  ): Promise<WebhookProcessingResultDto> {
    const { repository } = payload;

    // Remove repository from our database
    await this.db
      .delete(giteaRepositories)
      .where(
        and(
          eq(giteaRepositories.giteaProfileId, profile.id),
          eq(giteaRepositories.giteaRepoId, repository.id)
        )
      );

    this.logger.log(`Repository deleted and removed from sync: ${repository.full_name}`);

    return {
      processed: true,
      action: 'deleted',
      repository: repository.full_name,
      message: 'Repository deleted and removed from sync',
    };
  }

  private async handleRepositoryUpdated(
    profile: GiteaProfile,
    payload: GiteaWebhookPayload
  ): Promise<WebhookProcessingResultDto> {
    const { repository } = payload;

    // Re-sync the repository to get updated information
    await this.repositorySyncService.syncRepository(profile.id, repository as any);

    this.logger.log(`Repository updated and re-synced: ${repository.full_name}`);

    return {
      processed: true,
      action: 'updated',
      repository: repository.full_name,
      message: 'Repository updated and re-synced successfully',
    };
  }
}
