import { relations } from 'drizzle-orm';
import { boolean, index, integer, json, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { users } from './users.schema.js';

export const giteaProfiles = pgTable('gitea_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  
  // Gitea account information
  giteaUserId: integer('gitea_user_id').notNull().unique(),
  giteaUsername: varchar('gitea_username', { length: 100 }).notNull(),
  giteaEmail: varchar('gitea_email', { length: 255 }).notNull(),
  giteaFullName: varchar('gitea_full_name', { length: 255 }),
  giteaAvatarUrl: text('gitea_avatar_url'),
  
  // Integration status
  isActive: boolean('is_active').notNull().default(true),
  isProvisioned: boolean('is_provisioned').notNull().default(false),
  
  // Sync information
  lastSyncAt: timestamp('last_sync_at'),
  syncStatus: varchar('sync_status', { length: 50 }).default('pending'), // pending, syncing, completed, failed
  syncErrors: json('sync_errors').$type<string[]>(),
  
  // Repository statistics
  totalRepositories: integer('total_repositories').default(0),
  publicRepositories: integer('public_repositories').default(0),
  privateRepositories: integer('private_repositories').default(0),
  publishedRepositories: integer('published_repositories').default(0),
  
  // Profile metadata from Gitea
  giteaProfile: json('gitea_profile').$type<{
    bio?: string;
    website?: string;
    location?: string;
    company?: string;
    twitter?: string;
    linkedin?: string;
    followers?: number;
    following?: number;
    starredRepos?: number;
  }>(),
  
  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  userIdIdx: index('gitea_profiles_user_id_idx').on(table.userId),
  giteaUserIdIdx: index('gitea_profiles_gitea_user_id_idx').on(table.giteaUserId),
  giteaUsernameIdx: index('gitea_profiles_gitea_username_idx').on(table.giteaUsername),
  syncStatusIdx: index('gitea_profiles_sync_status_idx').on(table.syncStatus),
  isActiveIdx: index('gitea_profiles_is_active_idx').on(table.isActive),
}));

// Relations
export const giteaProfilesRelations = relations(giteaProfiles, ({ one, many }) => ({
  user: one(users, {
    fields: [giteaProfiles.userId],
    references: [users.id],
  }),
  repositories: many(giteaRepositories),
}));

// Import the repositories relation (will be defined in next file)
import { giteaRepositories } from './gitea-repositories.schema.js';

export type GiteaProfile = typeof giteaProfiles.$inferSelect;
export type NewGiteaProfile = typeof giteaProfiles.$inferInsert;
