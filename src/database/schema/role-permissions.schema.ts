import { pgTable, timestamp, uuid } from 'drizzle-orm/pg-core';
import { permissions } from './permissions.schema.js';
import { roles } from './roles.schema.js';

export const rolePermissions = pgTable('role_permissions', {
  id: uuid('id').primaryKey().defaultRandom(),
  roleId: uuid('role_id').notNull().references(() => roles.id),
  permissionId: uuid('permission_id').notNull().references(() => permissions.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
}); 